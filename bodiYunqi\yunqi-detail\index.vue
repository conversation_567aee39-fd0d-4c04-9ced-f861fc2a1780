<template>
	<view class="container">
		<!-- 顶部标题 -->
		<!-- <view class="header">
      <text class="header-title">{{detailData.leadName || '线索详情'}}</text>
    </view> -->

		<!-- Tab 切换 -->
		<view class="tab-container">
			<view class="tab-header">
				<view class="tab-item" :class="{'active': currentTab === 0}" @tap="switchTab(0)">
					<text class="tab-text">客户资料</text>
				</view>
				<view class="tab-item" :class="{'active': currentTab === 1}" @tap="switchTab(1)">
					<text class="tab-text">流程</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 客户资料 -->
			<view v-if="currentTab === 0" class="tab-content">
				<view class="section">
					<view class="section-title">相关负责人</view>
					<view class="info-card">
						<!-- 循环渲染所有负责人 -->
						<view class="info-row" v-for="(dutyItem, index) in dutyList" :key="index">
							<text class="info-label">{{dutyItem.name}}：</text>
							<text class="info-value">{{dutyItem.real_name}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">当前节点：</text>
							<text class="info-value">{{detailData.currentNode}}</text>
						</view>
					</view>
				</view>

				<view class="section">
					<view class="section-title">客户基本信息</view>
					<view class="info-card">
						<view class="info-row">
							<text class="info-label">客户姓名：</text>
							<text class="info-value">{{detailData.customerName}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">联系电话：</text>
							<text class="info-value">{{detailData.customerPhone || '暂未录入'}}</text>
						</view>
						<!-- <view class="info-row">
							<text class="info-label">客户类型：</text>
							<text class="info-value">{{detailData.customerType}}</text>
						</view> -->
						<view class="info-row">
							<text class="info-label">客户来源：</text>
							<text class="info-value">{{detailData.customerSource}}</text>
						</view>
                        <view class="info-row">
							<text class="info-label">客户地址：</text>
							<text class="info-value">{{detailData.customerAddress}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">项目总金额：</text>
							<text class="info-value">{{detailData.budgetRange}}</text>
						</view>
						<!-- <view class="info-row" v-if="detailData.area">
							<text class="info-label">面积：</text>
							<text class="info-value">{{detailData.area}}</text>
						</view> -->
						<!-- <view class="info-row" v-if="detailData.structure">
							<text class="info-label">户型：</text>
							<text class="info-value">{{detailData.structure}}</text>
						</view> -->
						<view class="info-row" v-if="detailData.unitPrice">
							<text class="info-label">单价：</text>
							<text class="info-value">{{detailData.unit_price}}/平米</text>
						</view>
						<view class="info-row" v-if="detailData.total">
							<text class="info-label">总价：</text>
							<text class="info-value">{{detailData.total}}元</text>
						</view>
						<view class="info-row" v-if="detailData.pay">
							<text class="info-label">已付款：</text>
							<text class="info-value">{{detailData.pay}}元</text>
						</view>
						<view class="info-row" v-if="detailData.surplus">
							<text class="info-label">待付款：</text>
							<text class="info-value">{{detailData.surplus}}元</text>
						</view>
						<!-- <view class="info-row" v-if="detailData.remark">
							<text class="info-label">备注信息：</text>
							<text class="info-value">{{detailData.remark}}</text>
						</view> -->
					</view>
				</view>
			</view>

			<!-- 流程 -->
			<view v-if="currentTab === 1" class="tab-content">
				<view class="section">
					<view class="section-title">流程信息</view>
					<view class="info-card">
						<view class="info-row">
							<text class="info-label">开启时间：</text>
							<text class="info-value">{{detailData.startTime}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">当前节点截止时间：</text>
							<text class="info-value">{{detailData.deadline}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">当前状态：</text>
							<text class="info-value status-text" :class="'status-' + detailData.status">
								{{getStatusText(detailData.status)}}
							</text>
						</view>
						<view class="info-row" v-if="detailData.alertInfo">
							<text class="info-label">报警信息：</text>
							<text class="info-value alert-text">{{detailData.alertInfo}}</text>
						</view>
					</view>
				</view>

				<view class="section">
					<view class="section-title">流程节点</view>
					<view class="process-tree">
						<view v-for="(node, nodeIndex) in processNodes" :key="nodeIndex" class="tree-node">
							<!-- 主节点 -->
							<view class="main-node">
								<view class="node-content" :class="'node-' + node.status" @tap="toggleNode(nodeIndex)">
									<view class="node-icon">
										<text class="icon-text" v-if="node.status === 'waiting'">⏸️</text>
										<text class="icon-text" v-else-if="node.status === 'pending'">⏳</text>
										<text class="icon-text" v-else-if="node.status === 'processing'">🔄</text>
										<text class="icon-text" v-else-if="node.status === 'completed'">✅</text>
										<text class="icon-text" v-else-if="node.status === 'rejected'">❌</text>
										<text class="icon-text" v-else-if="node.status === 'abandoned'">🗑️</text>
										<text class="icon-text" v-else-if="node.status === 'terminated'">⛔</text>
									</view>
									<view class="node-info">
										<text class="node-title">{{node.title}}</text>
										<text class="node-desc">{{node.description}}</text>
										<text class="node-time" v-if="node.realName">负责人：{{node.realName}}</text>
										<text class="node-time" v-if="node.duration">时长：{{node.duration}}小时</text>
									</view>
									<view class="node-status">
										<text class="status-text" :class="'status-' + node.status">
											{{node.originalState || getNodeStatusText(node.status)}}
										</text>
									</view>
									<!-- 折叠展开按钮 -->
									<view v-if="node.children && node.children.length > 0" class="toggle-btn">
										<text class="toggle-icon" :class="{'expanded': node.expanded}">▼</text>
									</view>
								</view>

								<!-- 连接线到子节点 -->
								<view v-if="node.children && node.children.length > 0 && node.expanded"
									class="connector-line"></view>
							</view>

							<!-- 子节点组 -->
							<view v-if="node.children && node.children.length > 0 && node.expanded" class="child-nodes">
								<view v-for="(child, childIndex) in node.children" :key="childIndex" class="child-node">
									<!-- 子节点连接线 -->
									<view class="child-connector">
										<view class="horizontal-line"></view>
										<view class="vertical-line" v-if="childIndex < node.children.length - 1"></view>
									</view>

									<!-- 子节点内容 -->
									<view class="child-content" :class="'node-' + child.status" @tap="handleChildNodeClick(child, nodeIndex, childIndex)">
										<view class="child-icon">
											<text class="icon-text" v-if="child.status === 'waiting'">⏸️</text>
											<text class="icon-text" v-else-if="child.status === 'pending'">⏳</text>
											<text class="icon-text" v-else-if="child.status === 'processing'">🔄</text>
											<text class="icon-text" v-else-if="child.status === 'completed'">✅</text>
											<text class="icon-text" v-else-if="child.status === 'rejected'">❌</text>
											<text class="icon-text" v-else-if="child.status === 'abandoned'">🗑️</text>
											<text class="icon-text" v-else-if="child.status === 'terminated'">⛔</text>
										</view>
										<view class="child-info">
											<text class="child-title">{{child.title}}</text>
											<text class="child-desc">{{child.description}}</text>
											<text class="child-time" v-if="child.realName">负责人：{{child.realName}}</text>
											<text class="child-time" v-if="child.duration">时长：{{child.duration}}小时</text>
										</view>
										<view class="child-status">
											<text class="status-text" :class="'status-' + child.status">
												{{child.originalState || getNodeStatusText(child.status)}}
											</text>
										</view>
										<!-- 可点击指示器 -->
										<view v-if="child.status !== 'rejected' && child.status !== 'abandoned' && child.status !== 'terminated'" class="click-indicator">
											<text class="arrow-icon">→</text>
										</view>
									</view>
								</view>
							</view>

							<!-- 主节点间的连接线 -->
							<view v-if="nodeIndex < processNodes.length - 1" class="main-connector"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		name: 'YunqiDetail',
		data() {
			return {
				currentTab: 0, // 当前选中的tab，0-客户资料，1-流程
				detailData: {},
				itemId: '',
				itemType: '',
				processNodes: [], // 流程节点数据
				dutyList: [] // 负责人列表
			}
		},
		onLoad(options) {
			console.log('详情页参数:', options)
			this.itemId = options.flow_custom_id
			this.loadDetailData()
		},
		methods: {
			// 切换tab
			switchTab(index) {
				this.currentTab = index
			},

			// 切换节点展开/折叠状态
			toggleNode(nodeIndex) {
				this.processNodes[nodeIndex].expanded = !this.processNodes[nodeIndex].expanded
			},

			// 处理子节点点击事件
			handleChildNodeClick(child, nodeIndex, childIndex) {
				// 禁用状态的节点不可点击
				if (child.status === 'rejected' || child.status === 'abandoned' || child.status === 'terminated') {
					uni.showToast({
						title: '该节点暂时禁用',
						icon: 'none'
					})
					return
				}

				// 跳转到表单填写页面，传入对应的id和state
				const params = {
					formType: child.formType || 'default',
					nodeId: child.id, // 子节点ID
					id: child.id, // 传入对应的id
					state: child.originalState || child.status, // 传入对应的state，优先使用原始状态
					status: child.status, // 保留原有的status参数
					title: encodeURIComponent(child.formTitle || child.title),
					leadId: this.detailData.id || this.itemId || '',
					flowId: child.flowId || '', // 流程ID
					level: child.level || '', // 节点层级
					nodeIndex: nodeIndex, // 主节点索引
					childIndex: childIndex, // 子节点索引
					realName: child.realName || '', // 负责人姓名
					duration: child.duration || '' // 时长
				}

				// 构建URL参数字符串
				const urlParams = Object.keys(params)
					.filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined)
					.map(key => `${key}=${params[key]}`)
					.join('&')

				console.log('跳转参数:', params)

				// 跳转到表单填写页面
				uni.navigateTo({
					url: `/bodiYunqi/form/index?${urlParams}`
				})
			},

			// 加载详情数据
			loadDetailData() {
                // EstateWork/flowdetail
                this.$api.request('EstateWork/flowdetail', { flow_custom_id: this.itemId }, (res) => {
					if (res.status === 'ok') {
						console.log('职能列表:', res)
					} else {
						uni.showToast({
						title: res.info,
						icon: 'none'
					})
					}
				})

				// 调用 work/profile 
				this.callWorkProfileApi()

				// 调用 work/flow
				this.callWorkFlowApi()

				// 注释掉模拟数据，使用真实API数据
				// this.loadMockData()
			},

			// 调用 work/profile 接口
			callWorkProfileApi() {
				this.$api.request('work/profile', {
					flow_custom_id: this.itemId // 使用当前项目ID，默认为2
				}, (res) => {
					console.log('work/profile 接口返回值:', res)

					if (res.status === 'ok') {
						// 将接口返回的数据填充到页面
						this.fillCustomerData(res)
					} else {
						uni.showToast({
							title: res.info || 'work/profile 接口调用失败',
							icon: 'none'
						})
					}
				})
			},

			// 调用 work/flow 接口
			callWorkFlowApi() {
				this.$api.request('EstateWork/flow', {
					flow_custom_id: this.itemId // 使用当前项目ID，默认为2
				}, (res) => {
					console.log('work/flow 接口返回值:', res)
					console.log('work/flow 接口返回值 JSON:', JSON.stringify(res, null, 2))

					if (res.status === 'ok') {
						// 处理流程节点数据
						this.processFlowData(res.flow || [])

						uni.showToast({
							title: 'work/flow 接口调用成功，数据已更新',
							icon: 'none',
							duration: 3000
						})
					} else {
						uni.showToast({
							title: res.info || 'work/flow 接口调用失败',
							icon: 'none'
						})
					}
				})
			},

			// 处理流程节点数据
			processFlowData(flowData) {
				// 将API返回的流程数据转换为页面需要的格式
				this.processNodes = flowData.map(flowItem => {
					return {
						id: flowItem.id,
						title: flowItem.work_name,
						description: flowItem.full_name,
						status: this.convertFlowState(flowItem.state),
						originalState: flowItem.state, // 保存原始状态
						time: '', // API未返回时间信息
						expanded: true, // 默认展开
						realName: flowItem.real_name,
						duration: flowItem.duration,
						level: flowItem.level,
						flowId: flowItem.flow_id,
						children: (flowItem.children || []).map(child => ({
							id: child.id,
							title: child.work_name,
							description: child.full_name,
							status: this.convertFlowState(child.state),
							originalState: child.state, // 保存原始状态
							time: '', // API未返回时间信息
							formType: child.form_type || 'default',
							formTitle: child.work_name,
							realName: child.real_name,
							duration: child.duration,
							level: child.level,
							flowId: child.flow_id,
							nodeId: child.node_id
						}))
					}
				})
			},

			// 转换流程状态
			convertFlowState(apiState) {
				const stateMap = {
					'待开启': 'waiting',
					'待提交': 'pending',
					'已提交': 'processing',
					'已完成': 'completed',
					'已驳回': 'rejected',
					'已废弃': 'abandoned',
					'已终止': 'terminated'
				}
				return stateMap[apiState] || 'waiting'
			},

			// 填充客户数据到页面
			fillCustomerData(apiResponse) {
				const data = apiResponse.data || {}
				const detail = apiResponse.detail || data
				const duty = apiResponse.duty || []

				// 更新负责人列表
				this.dutyList = duty

				// 获取销售人员信息
				const salesperson = duty.find(item => item.name === '销售')
				const process = duty.find(item => item.name != '销售')

				// 更新 detailData
				this.detailData = {
					id: detail.id || this.itemId,
					leadName: detail.flow_custom_name || detail.custom_name || '客户详情',
					salesperson: salesperson ? salesperson.real_name : '未分配',
					nodeOwner: process ? process.real_name : '未分配',
					currentNode: detail.current_flow || '未知节点',
					customerName: detail.custom_name || detail.flow_custom_name || '',
					customerPhone: detail.phone || '',
					// customerType: '个人客户', // 接口未返回，使用默认值
					// customerSource: '线上推广', // 接口未返回，使用默认值
					interestedProduct: '博笛智家', // 接口未返回，使用默认值
					budgetRange: detail.total ? `${detail.total}万` : '',
					customerAddress: this.formatAddress(detail),
					// remark: detail.remark || '',
					startTime: detail.start_open_date || '',
					// deadline: '', // 接口未返回
					status: 'active',
					alertInfo: '',
					// 新增字段显示更多信息
					pay: detail.pay || '0.00',
					surplus: detail.surplus || '0.00',
					unitPrice: detail.unit_price || '',
					structure: detail.structure || ''
				}
			},

			// 格式化地址
			formatAddress(detail) {
				const parts = []
				if (detail.province) parts.push(detail.province)
				if (detail.city) parts.push(detail.city)
				if (detail.area) parts.push(detail.area)
				if (detail.address) parts.push(detail.address)
				return parts.join(' ') || ''
			},

			// 加载模拟数据（作为备用数据）
			loadMockData() {
				// 模拟数据，实际应该根据 id 和 type 调用 API
				const mockData = {
					myTodo: {
						id: this.itemId,
						leadName: '张先生智能家居项目',
						salesperson: '小王',
						nodeOwner: '小王',
						currentNode: '需求确认',
						customerName: '张先生',
						customerPhone: '138****1234',
						customerType: '个人客户',
						customerSource: '线上推广',
						budgetRange: '10-20万',
						customerAddress: '北京市朝阳区xxx小区',
						remark: '客户对智能家居很感兴趣，希望能够实现全屋智能化',
						startTime: '2024-01-15 10:30',
						deadline: '2024-01-20 18:00',
						status: 'pending',
						alertInfo: '即将超时'
					},
					employeeTodo: {
						id: this.itemId,
						leadName: '王先生家庭影院方案',
						salesperson: '小李',
						nodeOwner: '小李',
						currentNode: '产品演示',
						customerName: '王先生',
						customerPhone: '137****9012',
						customerType: '个人客户',
						customerSource: '朋友推荐',
						interestedProduct: '家庭影院系统',
						budgetRange: '15-25万',
						customerAddress: '上海市浦东新区xxx小区',
						remark: '客户希望打造专业级家庭影院',
						startTime: '2024-01-15 14:00',
						deadline: '2024-01-17 16:00',
						status: 'pending',
						alertInfo: '正常进行'
					},
					myCustomer: {
						id: this.itemId,
						leadName: '张先生智能家居项目',
						salesperson: '小王',
						nodeOwner: '小王',
						currentNode: '客户跟进',
						customerName: '张先生',
						customerPhone: '138****1234',
						customerType: '个人客户',
						customerSource: '线上推广',
						interestedProduct: '智能家居系统',
						budgetRange: '10-20万',
						customerAddress: '北京市朝阳区xxx小区',
						remark: '客户对智能家居很感兴趣',
						startTime: '2024-01-10 09:15',
						deadline: '2024-01-25 18:00',
						status: 'active',
						alertInfo: ''
					},
					employeeCustomer: {
						id: this.itemId,
						leadName: '王先生家庭影院项目',
						salesperson: '小李',
						nodeOwner: '小李',
						currentNode: '方案设计',
						customerName: '王先生',
						customerPhone: '137****9012',
						customerType: '个人客户',
						customerSource: '朋友推荐',
						interestedProduct: '家庭影院系统',
						budgetRange: '15-25万',
						customerAddress: '上海市浦东新区xxx小区',
						remark: '客户希望打造专业级家庭影院',
						startTime: '2024-01-13 15:45',
						deadline: '2024-01-28 16:00',
						status: 'active',
						alertInfo: '等待客户确认'
					}
				}

				// 如果API数据加载失败，使用模拟数据作为备用
				if (!this.detailData.customerName) {
					this.detailData = mockData[this.itemType] || {}
				}
				this.loadProcessNodes()
			},

			// 加载流程节点数据
			loadProcessNodes() {
				// 模拟流程节点数据
				this.processNodes = [{
						id: 1,
						title: '客户接触',
						description: '初次接触客户，了解基本需求',
						status: 'completed',
						time: '2024-01-15 10:30',
						expanded: true, // 默认展开
						children: [{
								id: 11,
								title: '需求调研',
								description: '详细了解客户需求和预算',
								status: 'completed',
								time: '2024-01-15 11:00',
								formType: 'requirement_survey',
								formTitle: '客户需求调研表'
							},
							{
								id: 12,
								title: '竞品分析',
								description: '分析竞争对手方案',
								status: 'completed',
								time: '2024-01-15 14:30',
								formType: 'competitor_analysis',
								formTitle: '竞品分析报告'
							}
						]
					},
					{
						id: 2,
						title: '方案设计',
						description: '根据需求设计解决方案',
						status: 'processing',
						time: '2024-01-16 09:00',
						expanded: true, // 默认展开
						children: [{
								id: 21,
								title: '技术方案',
								description: '制定技术实现方案',
								status: 'completed',
								time: '2024-01-16 10:00',
								formType: 'technical_solution',
								formTitle: '技术方案设计表'
							},
							{
								id: 22,
								title: '报价方案',
								description: '制定详细报价单',
								status: 'processing',
								time: '2024-01-16 15:00',
								formType: 'quotation',
								formTitle: '项目报价单'
							},
							{
								id: 23,
								title: '风险评估',
								description: '评估项目实施风险',
								status: 'pending',
								time: '',
								formType: 'risk_assessment',
								formTitle: '项目风险评估表'
							}
						]
					},
					{
						id: 3,
						title: '方案确认',
						description: '客户确认最终方案',
						status: 'pending',
						time: '',
						expanded: false, // 默认折叠
						children: [{
								id: 31,
								title: '方案演示',
								description: '向客户演示解决方案',
								status: 'pending',
								time: '',
								formType: 'solution_demo',
								formTitle: '方案演示记录表'
							},
							{
								id: 32,
								title: '合同谈判',
								description: '商务条款谈判',
								status: 'rejected',
								time: '',
								formType: 'contract_negotiation',
								formTitle: '合同谈判记录表'
							}
						]
					},
					{
						id: 4,
						title: '合同签署',
						description: '签署正式合同',
						status: 'abandoned',
						time: '',
						expanded: false, // 默认折叠
						children: [{
								id: 41,
								title: '合同审核',
								description: '法务部门审核合同',
								status: 'abandoned',
								time: '',
								formType: 'contract_review',
								formTitle: '合同审核表'
							},
							{
								id: 42,
								title: '合同签字',
								description: '双方签署合同',
								status: 'terminated',
								time: '',
								formType: 'contract_signing',
								formTitle: '合同签署确认表'
							}
						]
					}
				]
			},

			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'pending': '待处理',
					'processing': '处理中',
					'completed': '已完成',
					'cancelled': '已取消',
					'active': '活跃客户',
					'potential': '潜在客户',
					'inactive': '非活跃',
					'lost': '流失客户'
				}
				return statusMap[status] || '未知'
			},

			// 获取节点状态文本
			getNodeStatusText(status) {
				const statusMap = {
					'waiting': '待开启',
					'pending': '待提交',
					'processing': '已提交',
					'completed': '已完成',
					'rejected': '已驳回',
					'abandoned': '已废弃',
					'terminated': '已终止'
				}
				return statusMap[status] || '未知'
			},

			// 获取原始状态文本（直接显示API返回的状态）
			getOriginalStatusText(node) {
				// 如果有原始状态信息，优先显示
				if (node.originalState) {
					return node.originalState
				}
				return this.getNodeStatusText(node.status)
			},

			// 编辑
			handleEdit() {
				uni.showToast({
					title: '跳转到编辑页面',
					icon: 'none'
				})
			},

			// 返回
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		background-color: #ffffff;
		padding: 30rpx;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.tab-container {
		background-color: #ffffff;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.tab-header {
		display: flex;
		align-items: center;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		position: relative;

		&.active {
			.tab-text {
				color: #007aff;
				font-weight: bold;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 4rpx;
				background-color: #007aff;
				border-radius: 2rpx;
			}
		}
	}

	.tab-text {
		font-size: 30rpx;
		color: #666666;
		transition: color 0.3s;
	}

	.content-container {
		padding: 20rpx;
	}

	.tab-content {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.section {
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		padding: 30rpx;
		background-color: #f8f9fa;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.info-card {
		padding: 30rpx;
	}

	.info-row {
		display: flex;
		align-items: flex-start;
		margin-bottom: 24rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.info-label {
		font-size: 28rpx;
		color: #666666;
		min-width: 160rpx;
		flex-shrink: 0;
	}

	.info-value {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
		line-height: 1.5;
	}

	.status-text {
		font-weight: 500;

		&.status-waiting {
			color: #8e8e93;
		}

		&.status-pending {
			color: #ff9500;
		}

		&.status-processing {
			color: #007aff;
		}

		&.status-completed {
			color: #34c759;
		}

		&.status-rejected {
			color: #ff3b30;
		}

		&.status-abandoned {
			color: #636366;
		}

		&.status-terminated {
			color: #af52de;
		}

		&.status-cancelled {
			color: #ff3b30;
		}

		&.status-active {
			color: #007aff;
		}

		&.status-potential {
			color: #ff9500;
		}
	}

	.alert-text {
		color: #ff3b30;
		font-weight: 500;
	}

	.process-desc {
		font-size: 28rpx;
		color: #999999;
		text-align: center;
		padding: 60rpx 30rpx;
		display: block;
	}

	/* 流程树形图样式 */
	.process-tree {
		padding: 30rpx;
	}

	.tree-node {
		position: relative;
		margin-bottom: 40rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.main-node {
		position: relative;
	}

	.node-content {
		display: flex;
		align-items: center;
		padding: 24rpx;
		border-radius: 12rpx;
		border: 2rpx solid;
		background-color: #ffffff;
		cursor: pointer;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}

		&.node-waiting {
			border-color: #8e8e93;
			background-color: #f8f9fa;
		}

		&.node-pending {
			border-color: #ff9500;
			background-color: #fff7e6;
		}

		&.node-processing {
			border-color: #007aff;
			background-color: #e6f3ff;
		}

		&.node-completed {
			border-color: #34c759;
			background-color: #e8f5e8;
		}

		&.node-rejected {
			border-color: #ff3b30;
			background-color: #ffe6e6;
		}

		&.node-abandoned {
			border-color: #636366;
			background-color: #f0f0f0;
		}

		&.node-terminated {
			border-color: #af52de;
			background-color: #f3e8ff;
		}

		&.node-disabled {
			border-color: #d1d1d6;
			background-color: #f2f2f7;
		}
	}

	.node-icon {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.icon-text {
		font-size: 32rpx;
	}

	.node-info {
		flex: 1;
		margin-right: 20rpx;
	}

	.node-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 8rpx;
	}

	.node-desc {
		font-size: 26rpx;
		color: #666666;
		display: block;
		margin-bottom: 8rpx;
	}

	.node-time {
		font-size: 24rpx;
		color: #999999;
		display: block;
	}

	.node-status {
		flex-shrink: 0;
	}

	.toggle-btn {
		margin-left: 16rpx;
		flex-shrink: 0;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&:active {
			background-color: rgba(0, 0, 0, 0.2);
		}
	}

	.toggle-icon {
		font-size: 24rpx;
		color: #666666;
		transition: transform 0.3s ease;

		&.expanded {
			transform: rotate(180deg);
		}
	}

	.status-text {
		font-size: 24rpx;
		font-weight: 500;
		padding: 6rpx 12rpx;
		border-radius: 12rpx;

		&.status-waiting {
			color: #8e8e93;
			background-color: #f8f9fa;
		}

		&.status-pending {
			color: #ff9500;
			background-color: #fff7e6;
		}

		&.status-processing {
			color: #007aff;
			background-color: #e6f3ff;
		}

		&.status-completed {
			color: #34c759;
			background-color: #e8f5e8;
		}

		&.status-rejected {
			color: #ff3b30;
			background-color: #ffe6e6;
		}

		&.status-abandoned {
			color: #636366;
			background-color: #f0f0f0;
		}

		&.status-terminated {
			color: #af52de;
			background-color: #f3e8ff;
		}

		&.status-disabled {
			color: #8e8e93;
			background-color: #f2f2f7;
		}
	}

	/* 连接线样式 */
	.connector-line {
		position: absolute;
		left: 50%;
		bottom: -20rpx;
		transform: translateX(-50%);
		width: 2rpx;
		height: 20rpx;
		background-color: #d1d1d6;
	}

	.main-connector {
		position: absolute;
		left: 50%;
		bottom: -40rpx;
		transform: translateX(-50%);
		width: 2rpx;
		height: 40rpx;
		background-color: #d1d1d6;
	}

	/* 子节点样式 */
	.child-nodes {
		margin-top: 20rpx;
		padding-left: 40rpx;
		position: relative;
		animation: slideDown 0.3s ease-out;
		transform-origin: top;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-20rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.child-node {
		position: relative;
		margin-bottom: 20rpx;
		display: flex;
		align-items: flex-start;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.child-connector {
		position: relative;
		width: 40rpx;
		height: 100%;
		flex-shrink: 0;
	}

	.horizontal-line {
		position: absolute;
		top: 50%;
		left: 0;
		width: 30rpx;
		height: 2rpx;
		background-color: #d1d1d6;
		transform: translateY(-50%);
	}

	.vertical-line {
		position: absolute;
		left: 0;
		top: 50%;
		width: 2rpx;
		height: 100%;
		background-color: #d1d1d6;
	}

	.child-content {
		flex: 1;
		display: flex;
		align-items: center;
		padding: 16rpx;
		border-radius: 8rpx;
		border: 1rpx solid;
		background-color: #ffffff;
		cursor: pointer;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}

		&.node-waiting {
			border-color: #8e8e93;
			background-color: #f8f9fa;

			&:hover {
				border-color: #7a7a7f;
				box-shadow: 0 2rpx 8rpx rgba(142, 142, 147, 0.2);
			}
		}

		&.node-pending {
			border-color: #ff9500;
			background-color: #fff7e6;

			&:hover {
				border-color: #e6850e;
				box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.2);
			}
		}

		&.node-processing {
			border-color: #007aff;
			background-color: #e6f3ff;

			&:hover {
				border-color: #0056cc;
				box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
			}
		}

		&.node-completed {
			border-color: #34c759;
			background-color: #e8f5e8;

			&:hover {
				border-color: #28a745;
				box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.2);
			}
		}

		&.node-rejected {
			border-color: #ff3b30;
			background-color: #ffe6e6;

			&:hover {
				border-color: #e6342a;
				box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.2);
			}
		}

		&.node-abandoned {
			border-color: #636366;
			background-color: #f0f0f0;

			&:hover {
				border-color: #565659;
				box-shadow: 0 2rpx 8rpx rgba(99, 99, 102, 0.2);
			}
		}

		&.node-terminated {
			border-color: #af52de;
			background-color: #f3e8ff;

			&:hover {
				border-color: #9c47c7;
				box-shadow: 0 2rpx 8rpx rgba(175, 82, 222, 0.2);
			}
		}

		&.node-disabled {
			border-color: #e5e5ea;
			background-color: #fafafa;
			cursor: not-allowed;
			opacity: 0.6;
		}
	}

	.child-icon {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
		flex-shrink: 0;

		.icon-text {
			font-size: 24rpx;
		}
	}

	.child-info {
		flex: 1;
		margin-right: 16rpx;
	}

	.child-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		display: block;
		margin-bottom: 6rpx;
	}

	.child-desc {
		font-size: 24rpx;
		color: #666666;
		display: block;
		margin-bottom: 6rpx;
	}

	.child-time {
		font-size: 22rpx;
		color: #999999;
		display: block;
	}

	.child-status {
		flex-shrink: 0;

		.status-text {
			font-size: 22rpx;
			padding: 4rpx 8rpx;
		}
	}

	.click-indicator {
		margin-left: 12rpx;
		flex-shrink: 0;
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(0, 122, 255, 0.1);
		transition: all 0.3s ease;
	}

	.arrow-icon {
		font-size: 20rpx;
		color: #007aff;
		font-weight: bold;
	}
</style>