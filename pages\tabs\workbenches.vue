<template>
	<view class="container">
		<!-- #ifdef MP -->
		<view style="height: 220rpx;width: 100%;background-color: #f5f5f5;position: fixed;top: 0;z-index: 10;">
			<!-- 选项卡 15117978015-->
			
			<view class="tabs">
				<view class="tab-item" :class="{ active: activeTab === 0 }" @click="changeTab(0)">
					<text>全部</text>
					<view class="badge">{{count}}</view>
					<view v-if="activeTab === 0" class="active-line"></view>
				</view>
				<view class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
					<text>今日需跟进</text>
					<view class="badge">{{ count2 }}</view>
					<view v-if="activeTab === 1" class="active-line"></view>
				</view>
				<view class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
					<text>今日已跟进</text>
					<view class="badge">{{ count1 }}</view>
					<view v-if="activeTab === 2" class="active-line"></view>
				</view>
			</view>
			<!-- 搜索框 -->
			<view class="search-container">
				<view class="search-box">
					<view class="tn-icon-filter justify-content-item tn-padding-right-xs tn-text-lg" style="color: #3467C2;width: 30px; height: 100%; display: flex;align-items: center;" @click="openSelectModal()"></view>
					<input type="text" placeholder="请输入搜索关键词" placeholder-class="placeholder" v-model="searchKey" />
				</view>
				<view class="action-buttons">
					<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="search()">
						<i class="tn-icon-search" aria-hidden="true"></i>
					</tn-button>
					<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="AddCustomer()">
						<i class="tn-icon-add" aria-hidden="true"></i>
					</tn-button>
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<view style="height: 220rpx;width: 100%;background-color: #f5f5f5;position: fixed;top: 88rpx !important;z-index: 10;">
			<view class="tabs">
				<view class="tab-item" :class="{ active: activeTab === 0 }" @click="changeTab(0)">
					<text>全部</text>
					<view class="badge">{{count}}</view>
					<view v-if="activeTab === 0" class="active-line"></view>
				</view>
				<view class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
					<text>今日需跟进</text>
					<view class="badge">{{ count2 }}</view>
					<view v-if="activeTab === 1" class="active-line"></view>
				</view>
				<view class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
					<text>今日已跟进</text>
					<view class="badge">{{ count1 }}</view>
					<view v-if="activeTab === 2" class="active-line"></view>
				</view>
			</view>
			
			<!-- 搜索框 -->
			<view class="search-container">
				<view class="search-box">
					<view class="tn-icon-filter justify-content-item tn-padding-right-xs tn-text-lg" style="color: #3467C2;width: 30px; height: 100%; display: flex;align-items: center;" @click="openSelectModal()"></view>
					<input type="text" placeholder="请输入搜索关键词" placeholder-class="placeholder" v-model="searchKey" />
				</view>
				<view class="action-buttons">
					<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="search()">
						<i class="tn-icon-search" aria-hidden="true"></i>
					</tn-button>
					<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="AddCustomer()">
						<i class="tn-icon-add" aria-hidden="true"></i>
					</tn-button>
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- 列表内容 -->
		<view class="list-container" v-if="filteredListData.length">
			<view class="list-item" v-for="(item, index) in filteredListData" :key="index">
				<view class="item-header">
					<view class="item-title" style="display: flex;gap: 10rpx;align-items: center;">
						<view v-if="item.new_allocat_data" style="width: 26rpx;height: 26rpx;background: red;border-radius: 50%;"></view>
						{{ item.clue_name }}
					</view>
					<view class="item-tag" :style="{color: item.new_followup_status_name == '未跟进' ? '#FFB7A1' : item.new_followup_status_name == '有效' ? '#A4E82F' : item.new_followup_status_name == '无效' ? '#E83A30' : '#9EBEFF' }">{{ item.new_followup_status_name }}</view>
				</view>
				<view class="item-info">
					<view class="info-grid">
						<view class="info-grid-item">
							<text class="info-label">电话：</text>
							<text class="info-value">
								{{ item.phone }}
								<text class="tn-icon-copy-fill" style="margin-left: 10rpx;" @click="copyText(item.phone)"></text>
							</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">来源：</text>
							<text class="info-value">{{ item.channel }}</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">区域：</text>
							<text class="info-value">{{ item.area }}</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">所属人：</text>
							<text class="info-value">{{ item.belongUserName }}</text>
						</view>
					</view>
					<view class="info-grid-items" v-if="item.last_log_center">
						<!-- <text class="info-label">最新跟进记录：</text> -->
						<text class="info-values" style="font-size: 24rpx;color: skyblue;">{{ item.last_log_center }}</text>
					</view>
					
				</view>

				<view class="item-divider"></view>
				<view class="item-footer">
					<view class="action-btns">
						<view class="action-btn" v-for="(btnItem, btnIndex) in item.btn" :key="btnIndex">
							<!-- 电话类型按钮 -->
							<tn-button :backgroundColor="btnItem.color" fontColor="#fff" size="sm" v-if="btnItem.type === 'tel'" @click="handleTel(btnItem.param.tel)">
								<text :class="btnItem.icon"></text>
								<text class="text">{{ btnItem.title }}</text>
							</tn-button>
							<tn-button :backgroundColor="btnItem.color" fontColor="#fff" size="sm" v-if="['navigate', 'redirect', 'reLaunch', 'switch'].includes(btnItem.type)" @click="handleNavigate(btnItem.path, btnItem.param, item.id)">
								<text :class="btnItem.icon"></text>
								<text class="text">{{ btnItem.title }}</text>
							</tn-button>
							<tn-button :backgroundColor="btnItem.color" fontColor="#fff" size="sm" v-if="btnItem.type === 'func'" @click="handleFunc(btnItem.api, btnItem.path, btnItem.param, item.id)">
								<text :class="btnItem.icon"></text>
								<text class="text">{{ btnItem.title }}</text>
							</tn-button>
							<tn-button :backgroundColor="btnItem.color" fontColor="#fff" size="sm" v-if="btnItem.type === 'prompt'" @click="handlePrompt(btnItem)">
								<text :class="btnItem.icon"></text>
								<text class="text">{{ btnItem.title }}</text>
							</tn-button>
						</view>
					</view>
				</view>
				<image v-if="item.is_submit == '已提交'" style="position: absolute;bottom: 0rpx;width: 90rpx;height: 90rpx;right: 0rpx;transform: rotate(-50deg);" src="/static/tijiao.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="datas" v-else>
			<tn-empty mode="data"></tn-empty>
		</view>
		<!-- 分配模态框 -->
		<!-- <view class="assign-modal" v-if="showAssignModal">
			<view class="modal-mask" @click="closeAssignModal"></view>
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择分配用户</text>
					<text class="modal-close" @click="closeAssignModal">×</text>
				</view>
				<view class="modal-body">
					<view class="user-btn-group">
						<view class="user-btn" v-for="(user, index) in assignUsers" :key="index"
							:class="{ 'active-btn': selectedUserId === user.value }"
							@click="selectUser(user.value, user.label)">
							{{ user.label }}
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn-cancel" @click="closeAssignModal">取消</button>
					<button class="btn-confirm" @click="confirmAssign">
						确认分配
					</button>	
				</view>	
			</view>
		</view> -->
		<!-- 在列表容器末尾添加 -->
		<view class="loading-status">
			<text v-if="isLoading">正在加载...</text>
			<text v-if="noMoreData">没有更多数据了</text>
		</view>

		<!-- <select-modal v-model="showSelectModal"></select	-modal> -->
		<SelectModal :value="showSelectModal" @input="toggleFilter" @filter="applyFilters" :listArrs="listArrs" :userInfoList="userInfoList" :key="listKey" v-if="listKey" :names="'crm'" />
	</view>


</template>

<script>
	import SelectModal from '@/components/select.vue'
	export default {
		components: {
			SelectModal
		},
		data() {
			return {
				
				activeTab: 0,
				showSelectModal: false,
				allData: [],
				filteredListData: [],
				count: '',
				todayneedcount: '',
				todaycount: '',
				searchKey: '',
				currentPage: 1, // 当前页码
				pageSize: 10, // 每页条数
				total: 100, // 总数据量
				isLoading: false, // 加载状态
				noMoreData: false, // 是否没有更多数据
				followupAt: null, // 筛选参数
				count1: 0,
				count2: 0,
				listArrs: {},
				listKey: 0,
				userInfoList: [],
				follow_status: '',
				customer_source: '',
                high_seas: '',
				belong_user_ids: '',
				regions: '',
				sxList: {
					structure: "",
					sex: "",
					income_source: "",
					housing_style: "",
					homestead: "",
					housing_validity: "",
					approval_status: "",
					layers: "",
					budget: "",
					intention: "",
					next_followup_at: "",
					followup_at: ''
				}
			}
		},
		props: {
		    nameKey: {
		      type: Number,
		      default: 1
		    },
			showTrue: {
		      type: Number,
		      default: 1
		    }
		  },
		  watch: {
		    // 监听 nameKey 的变化
		    nameKey(newVal, oldVal) {
		      if(this.total > this.pageSize){
		      	this.pageSize += 10
		      	this.isLoading = true
		      	this.fetchData();
		      } else{
		      	this.noMoreData = false
		      }
		    },
			showTrue(n, v){
				this.fetchData();
			}
		  },
		mounted() {
			this.fetchAssignUsers()
			this.fetchData();
		},
		
		onShow() {
			this.fetchAssignUsers()
			this.fetchData();
		},
		onReachBottom() {
			console.log('触发滚动到底部事件', this.total < this.pageSize) // 调试日志
			// if (!this.noMoreData) {
				
			// }
			if(this.total > this.pageSize){
				this.pageSize += 10
				this.isLoading = true
				this.fetchData();
			} else{
				this.noMoreData = false
			}
		},
		computed: {
			// 今日需跟进数量
			// todayNeedCount() {
			// 	return this.allData.filter(item => item.todayneedfollo === 1).length;
			// },
			// // 今日已跟进数量
			// todayCount() {
			// 	return this.allData.filter(item => item.todayfollow === 1).length;
			// }
		},
		methods: {
			copyText(text) {
			      uni.setClipboardData({
			        data: text, // 要复制的文本内容
			        success: () => {
			          uni.showToast({
			            title: '复制成功',
			            icon: 'none'
			          });
			        },
			        fail: (err) => {
			          uni.showToast({
			            title: '复制失败',
			            icon: 'none'
			          });
			          console.error('复制失败:', err);
			        }
			      });
			    },
			getUserInfo(){
				// user/getDevUserList
				this.$api.request('user/getDevUserList', {}, (res)=>{
					if (res.status == 'ok') {
						console.log(res, 'Uint16Array3')
						this.userInfoList = res.user_info
						this.fetchOptions();
					}
				})
			},
			fetchOptions() {
				this.$api.request('common/searchCustomerTermList', {}, (res)=>{
					if (res.status === 'ok') {
						this.listArrs = res.list
						this.listKey = this.listKey + 1
						console.log(this.listArrs, 'this.listArrs')
					}
				})
			},
			toggleFilter(value) {
				this.showSelectModal = value;
			},
			applyFilters(filters) {
				// 根据筛选条件进行筛选操作
				console.log('筛选条件:', filters);
				this.regions = filters.area instanceof Array ? filters.area.join(',') : ''
				this.follow_status = filters.status 
                this.high_seas = filters.high_seas
				this.customer_source = filters.channel instanceof Array ? filters.channel.join(',') : ''
				this.belong_user_ids = filters.user instanceof Array ? filters.user.join(',') : ''
				this.pageSize = 10
				this.sxList.structure = filters.structure instanceof Array ? filters.structure.join(',') : ''
				this.sxList.sex = filters.sex
				this.sxList.income_source = filters.income_source instanceof Array ? filters.income_source.join(',') : ''
				this.sxList.housing_style = filters.housing_style instanceof Array ? filters.housing_style.join(',') : ''
				this.sxList.homestead = filters.homestead instanceof Array ? filters.homestead.join(',') : ''
				this.sxList.housing_validity = filters.housing_validity instanceof Array ? filters.housing_validity.join(',') : ''
				this.sxList.approval_status = filters.approval_status instanceof Array ? filters.approval_status.join(',') : ''
				this.sxList.layers = filters.layers instanceof Array ? filters.layers.join(',') : ''
				this.sxList.budget = filters.budget instanceof Array ? filters.budget.join(',') : ''
				this.sxList.intention = filters.intention instanceof Array ? filters.intention.join(',') : ''
				this.sxList.followup_at = filters.next_followup_at
				
				console.log(this.regions, this.follow_status, this.customer_source, this.belong_user_ids )
				this.fetchData();
				this.showSelectModal = false;
			},
			// 打开选择弹框
			openSelectModal() {
				this.showSelectModal = true
			},

			// 打开分配模态框
			openfenpeiModal(clueId) {
				this.clueId = clueId;
				this.fetchAssignUsers();
				this.showAssignModal = true;
			},
			
			// 选择用户
			selectUser(userId, userLabel) {
				this.selectedUserId = userId;
				this.selectedUserLabel = userLabel;
			},

			// 获取可分配用户列表
			fetchAssignUsers() {
				const token = uni.getStorageSync('token');
				this.assignLoading = true;
				this.$api.request('user/getUserList', {}, (res)=>{
					if(res.status == 'ok'){
						this.getUserInfo()
						this.assignLoading = false;
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.assignLoading = false;
					}
				})
			},


			// 确认分配
			confirmAssign() {
				if (!this.selectedUserId) {
					return uni.showToast({
						title: '请选择分配用户',
						icon: 'none'
					});
				}

				uni.showModal({
					title: '确认分配',
					content: `确认将该线索分配给 ${this.selectedUserLabel}？`,
					success: (res) => {
						if (res.confirm) {
							this.assignClue();
						}
					}
				});
			},

			// 关闭分配模态框（重置选中状态）
			closeAssignModal() {
				this.showAssignModal = false;
				this.selectedUserId = null; // 清除选中状态
			},



			// 执行分配操作
			assignClue() {
				const token = uni.getStorageSync('token');
				this.$api.request('customer/assign', {clueId: this.clueId, userId: this.selectedUserId}, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							title: '分配成功'
						});
						this.showAssignModal = false;
						this.fetchData(); // 重新获取数据
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.assignLoading = false;
					}
				})
			},
			search() {
				this.currentPage = 1; // 重置页码
				this.pageSize = 10
				// this.noMoreData = false;
				this.fetchData(); // 重新获取数据
			},
			// 从接口获取数据
			fetchData() {
				let that = this
				
				this.$api.request('customer/listing', { 
						pageNo: this.currentPage,
						pageSize: this.pageSize,
						type: this.activeTab,
						follow_status: this.follow_status,
                        high_seas: this.high_seas,
						customer_source: this.customer_source,
						belong_user_ids: this.belong_user_ids,
						regions: this.regions,
						search: this.searchKey,
						structure: this.sxList.structure,
						sex: this.sxList.sex,
						income_source: this.sxList.income_source,
						housing_style: this.sxList.housing_style,
						homestead: this.sxList.homestead,
						housing_validity: this.sxList.housing_validity,
						approval_status: this.sxList.approval_status,
						layers: this.sxList.layers,
						budget: this.sxList.budget,
						intention: this.sxList.intention,
						followup_at: this.sxList.followup_at
					}, (res)=>{
					if(res.status == 'ok'){
						that.isLoading = false
						that.allData = res.list;
						that.count = res.total
						that.total = res.count
						that.todayneedcount = res.todayneedcount;
						that.todaycount = res.todaycount;
						that.count2 = res.todayneedfolloCount	
						that.count1 = res.todayfollowCount
						this.filteredListData = res.list;
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			
			// 修改过滤方法
			filterData() {
				let filtered = this.allData;

				// 选项卡过滤
				if (this.activeTab === 1) {
					filtered = filtered.filter(item => item.todayneedfollo == 1);
				} else if (this.activeTab === 2) {
					filtered = filtered.filter(item => item.todayfollow === 1);
				}

				// 搜索过滤（支持多字段匹配）
				if (this.searchKey.trim()) {
					const keyword = this.searchKey.trim().toLowerCase();
					filtered = filtered.filter(item =>
						item.clue_name.toLowerCase().includes(keyword) ||
						item.phone.includes(keyword) ||
						item.user_name.toLowerCase().includes(keyword)
					);
				}

				this.filteredListData = filtered;
			},


			changeTab(index) {
				if (this.activeTab === index) return;

				this.activeTab = index;
				this.currentPage = 1; // 重置页码
				this.pageSize = 10; // 重置页码
				this.searchKey = '';
				this.noMoreData = false; // 重置数据状态
				this.allData = []; // 清空旧数据
				this.fetchData();
				
			},

			//添加客户
			AddCustomer() {
				uni.navigateTo({
					url: '/crm/customer/addCustomer',
				})
			},

			// 拨打电话处理
			handleTel(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				});
			},

			// 跳转页面处理
			handleNavigate(path, param, id) {
				// const query = this.objectToQuery(param);
				uni.navigateTo({
					url: `${path}?id=${id}`
				});
			},
			// 修改
			modify(path, id) {
				uni.navigateTo({
					url: path + '?id=' + id
				});
				
			},
			// 执行功能处理
			handleFunc(api, path, param, id) {
				
				uni.showModal({
					title: '确认提交',
					content: param.desc,
					success: (res) => {
						if (res.confirm) {
							// 这里可以添加请求接口逻辑
							this.$api.request(api, {id: id}, (res1)=>{
								if(res1.status == 'ok'){
									uni.showToast({
										icon: 'none',
										title: res1.info
									})
                                    if(path){
                                        const timer = setTimeout(()=>{
                                            uni.navigateTo({
                                                url: path
                                            })
                                            clearTimeout(timer)
                                        }, 500)
                                    } else{
                                        this.fetchData();
                                    }
									
									
								} else{
									uni.showToast({
										icon: 'none',
										title: res1.info
									})
								}
								// this.fetchData();
								
							} )	
						}
					}
				});
				
				// uni.request({
				// 	url: api,
				// 	method: 'POST',
				// 	data: param,
				// 	success: (res) => {
						
				// 		this.fetchData();
				// 	}
				// });
			},

			// 对象转查询字符串
			objectToQuery(obj) {
				return Object.entries(obj).map(([k, v]) => `${k}=${v}`).join('&');
			},




		}
	}
</script>

<style lang="scss" scoped>
	.datas{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		margin: auto;
		width: 200px;  /* 需指定宽度 */
		height: 100px; /* 需指定高度 */
	}
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		padding: 30rpx 0;
		text-align: center;
		background-color: #ffffff;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.tabs {
		display: flex;
		justify-content: space-around;
		background-color: #ffffff;
		padding-bottom: 10rpx;
	}

	.tab-item {
		position: relative;
		padding: 20rpx 0;
		flex: 1;
		text-align: center;
		font-size: 30rpx;
		color: #333;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.active {
		color: #00AAFF;
		font-weight: bold;
	}

	.active-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #00AAFF;
		border-radius: 3rpx;
	}

	.badge {
		background-color: #FF4D4F;
		color: white;
		font-size: 24rpx;
		height: 36rpx;
		min-width: 36rpx;
		padding: 0 6rpx;
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 6rpx;
	}

	.search-container {
		padding: 20rpx;
		display: flex;
		align-items: center;
	}

	.search-box {
		flex: 1;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		border: 1rpx solid #EEEEEE;
	}

	.search-box input {
		flex: 1;
		height: 80rpx;
		margin-left: 10rpx;
		font-size: 28rpx;
	}

	.placeholder {
		color: #AAAAAA;
		font-size: 28rpx;
	}

	.action-buttons {
		display: flex;
		margin-left: 20rpx;
		gap: 20rpx;
		/* 使用Flex gap属性 */
	}

	.search-btn,
	.add-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #A5CAFF;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
	}

	.list-container {
		margin-top: 220rpx;
		flex: 1;
		padding: 0 20rpx;
		position: relative;
		z-index: 5;
	}

	.list-item {
		position: relative;
		z-index: 999;
		background-color: #ffffff;
		// background: rgba(165, 202, 255, 0.3);
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		// border: 1rpx solid rgba(165, 202, 255, 0.1);
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		position: relative;
	}
	// .bookmark{
	// 	position: absolute;
	// 	top: -20rpx;
	// 	right: -20rpx;
	// 	width: 100rpx;
	// 	height: 50rpx;
	// 	line-height: 50rpx;
	// 	text-align: center;
	// 	background: plum;
	// 	border-radius: 0 0 0 15rpx;
	// 	font-size: 24rpx;
	// 	color: #A5CAFF;
	// 	background-color: rgba(165, 202, 255, 0.1);
	// }
	.item-title {
		width: 80%;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.item-tag {
		font-size: 30rpx;
		color: #A5CAFF;
		// background-color: rgba(165, 202, 255, 0.1);
		// padding: 4rpx 12rpx;
		// border-radius: 4rpx;
	}

	.item-info {
		margin-bottom: 16rpx;
		position: relative;
	}

	.info-grid {
		
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15rpx 30rpx;
	}

	.info-grid-item {
		display: flex;
		align-items: center;
		font-size: 26rpx;
	}
	.info-grid-items {
		display: flex;
		width: 100%;
		align-items: center;
		font-size: 26rpx;
		margin-top: 15rpx;
		.info-label {
			color: #999;
			min-width: 100rpx;
			flex-shrink: 0;
		}
		.info-value {
			color: #666;
			flex: 1;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.info-values {
			color: #666;
			width: 100%;
		}
	}
	.info-label {
		color: #999;
		min-width: 100rpx;
		flex-shrink: 0;
	}
	.info-values {
		color: #666;
		width: 100%;
	}

	.info-value {
		color: #666;
		flex: 1;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.item-divider {
		position: relative;
		z-index: 999;
		height: 1rpx;
		background-color: #f0f0f0;
		margin: 10rpx 0 16rpx 0;
	}

	.item-footer {
		position: relative;
		z-index: 999;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.action-btns {
		display: flex;
		gap: 10rpx;
		flex-wrap: wrap;
		/* 添加换行功能 */
		max-width: 100%;
		/* 可选：限制容器最大宽度 */
	}

	.action-btn {
		padding: 16rpx 5rpx 0;
		border-radius: 10rpx;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 90rpx;
		flex-shrink: 0;
		/* 防止按钮被压缩 */
	}

	.view-btn {
		background-color: #f0f7ff;
		color: #3467C2;
		border: 1rpx solid #d6e6ff;
	}

	.call-btn {
		background-color: #e6f7f0;
		color: #27ae60;
		border: 1rpx solid #c8ebd7;
	}

	.edit-btn {
		background-color: #fff7e6;
		color: #f39c12;
		border: 1rpx solid #ffeac8;
	}

	.tab-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		border-top: 1rpx solid #EEEEEE;
	}

	.tab-bar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.tab-text {
		font-size: 24rpx;
		color: #999999;
		margin-top: 6rpx;
	}

	.active-text {
		color: #00AAFF;
	}

	.loading-status {
		text-align: center;
		padding: 60rpx;
		padding-bottom: 100rpx;
		color: #999;
		font-size: 28rpx;
	}

	// 点击态效果
	.action-btn {
		transition: all 0.2s;

		&--active {
			transform: scale(0.95);
			opacity: 0.8;
		}

		// 不同按钮的激活态颜色
		&.view-btn--active {
			background-color: #d6e6ff !important;
		}

		&.call-btn--active {
			background-color: #c8ebd7 !important;
		}

		&.edit-btn--active {
			background-color: #ffeac8 !important;
		}
	}


	//弹出层
	.assign-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 999;

		.modal-mask {
			position: absolute;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.5);
		}

		.modal-content {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 90%;
			max-width: 600rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 40rpx;

			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;

				.modal-title {
					font-size: 36rpx;
					font-weight: bold;
				}

				.modal-close {
					font-size: 40rpx;
					color: #999;
					cursor: pointer;
				}
			}

			.modal-body {
				max-height: 400rpx;
				overflow-y: auto;
				margin-bottom: 30rpx;
			}

			.user-btn-group {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				gap: 15rpx;
			}

			.user-btn {
				padding: 12rpx 24rpx;
				border: 1rpx solid #e0e0e0;
				border-radius: 8rpx;
				font-size: 28rpx;
				transition: all 0.2s;
				min-width: auto;

				&.active-btn {
					background-color: #007AFF !important; // 添加 !important 强制应用样式
					color: #fff !important;
					border-color: #007AFF !important;
				}

				&:hover {
					background-color: #f5f5f5;
				}
			}

			.modal-footer {
				display: flex;
				justify-content: flex-end;
				gap: 20rpx;
				margin-top: 30rpx;

				.btn-cancel {
					padding: 15rpx 30rpx;
					border: 1rpx solid #ddd;
					border-radius: 10rpx;
					background-color: #fff;
					font-size: 30rpx;
					cursor: pointer;
				}

				.btn-confirm {
					padding: 15rpx 30rpx;
					background-color: #007AFF; // 确认分配按钮颜色
					color: #fff;
					border-radius: 10rpx;
					font-size: 30rpx;
					cursor: pointer;
				}
			}
		}
	}
</style>