<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="header-title">{{formTitle}}</text>
      <view class="status-badge" :class="'status-' + nodeStatus">
        <text class="status-text">{{getStatusText(nodeStatus)}}</text>
      </view>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-section" v-for="(section, sectionIndex) in formConfig.sections" :key="sectionIndex">
        <view class="section-title">{{section.title}}</view>
        
        <view class="form-fields">
          <view v-for="(field, fieldIndex) in section.fields" :key="fieldIndex" class="field-item">
            <!-- 文本输入框 -->
            <view v-if="field.type === 'input'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <input 
                class="field-input" 
                :type="field.inputType || 'text'"
                :placeholder="field.placeholder"
                v-model="formData[field.key]"
                :disabled="field.disabled"
              />
            </view>
            
            <!-- 多行文本 -->
            <view v-else-if="field.type === 'textarea'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <textarea 
                class="field-textarea" 
                :placeholder="field.placeholder"
                v-model="formData[field.key]"
                :disabled="field.disabled"
                :maxlength="field.maxLength || 500"
              />
            </view>
            
            <!-- 选择器 -->
            <view v-else-if="field.type === 'picker'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <picker 
                :range="field.options" 
                :range-key="field.optionKey || 'label'"
                @change="handlePickerChange($event, field.key)"
                :disabled="field.disabled"
              >
                <view class="field-picker">
                  <text class="picker-text" :class="{'placeholder': !formData[field.key]}">
                    {{getPickerDisplayText(field, formData[field.key]) || field.placeholder}}
                  </text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
            
            <!-- 数字输入 -->
            <view v-else-if="field.type === 'number'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <input 
                class="field-input" 
                type="digit"
                :placeholder="field.placeholder"
                v-model="formData[field.key]"
                :disabled="field.disabled"
              />
            </view>
            
            <!-- 日期选择 -->
            <view v-else-if="field.type === 'date'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <picker
                mode="date"
                @change="handleDateChange($event, field.key)"
                :disabled="field.disabled"
              >
                <view class="field-picker">
                  <text class="picker-text" :class="{'placeholder': !formData[field.key]}">
                    {{formData[field.key] || field.placeholder}}
                  </text>
                  <text class="picker-arrow">📅</text>
                </view>
              </picker>
            </view>

            <!-- 图片上传 -->
            <view v-else-if="field.type === 'image'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <view class="image-upload-container">
                <view v-if="formData[field.key]" class="image-preview">
                  <image :src="formData[field.key]" class="preview-image" mode="aspectFit"></image>
                  <view class="image-actions">
                    <text class="action-btn" @tap="previewImage(formData[field.key])">预览</text>
                    <text class="action-btn delete-btn" @tap="removeImage(field.key)">删除</text>
                  </view>
                </view>
                <view v-else class="upload-placeholder" @tap="chooseImage(field.key)">
                  <text class="upload-icon">📷</text>
                  <text class="upload-text">{{field.placeholder}}</text>
                </view>
              </view>
            </view>

            <!-- 视频上传 -->
            <view v-else-if="field.type === 'video'" class="field-wrapper">
              <text class="field-label">
                {{field.label}}
                <text v-if="field.required" class="required-mark">*</text>
              </text>
              <view class="video-upload-container">
                <view v-if="formData[field.key]" class="video-preview">
                  <video :src="formData[field.key]" class="preview-video" controls></video>
                  <view class="video-actions">
                    <text class="action-btn delete-btn" @tap="removeVideo(field.key)">删除</text>
                  </view>
                </view>
                <view v-else class="upload-placeholder" @tap="chooseVideo(field.key)">
                  <text class="upload-icon">🎥</text>
                  <text class="upload-text">{{field.placeholder}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作按钮 -->
    <view class="footer-actions">
      <view class="action-btn secondary-btn" @tap="goBack">
        <text class="btn-text">返回</text>
      </view>
      <view class="action-btn primary-btn" @tap="handleSubmit" v-if="nodeStatus !== 'completed'">
        <text class="btn-text">{{nodeStatus === 'processing' ? '更新' : '提交'}}</text>
      </view>
      <view class="action-btn success-btn" v-else>
        <text class="btn-text">已完成</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormIndex',
  data() {
    return {
      formType: '',
      nodeId: '',
      id: '', // 新增：节点ID
      state: '', // 新增：节点状态
      formTitle: '',
      nodeStatus: '',
      leadId: '',
      flowId: '', // 新增：流程ID
      level: '', // 新增：节点层级
      nodeIndex: '', // 新增：主节点索引
      childIndex: '', // 新增：子节点索引
      realName: '', // 新增：负责人姓名
      duration: '', // 新增：时长
      parentNodeId: '', // 新增：父节点ID
      childNodeId: '', // 新增：子节点ID
      formConfig: {
        sections: []
      },
      formData: {}
    }
  },
  onLoad(options) {
    console.log('=== 表单页接收到的所有参数 ===')
    console.log('原始参数对象:', options)
    console.log('参数详情:')

    // 获取并打印所有参数
    this.formType = options.formType || ''
    this.nodeId = options.nodeId || ''
    this.id = options.id || '' // 对应的id
    this.state = options.state || '' // 对应的state
    this.formTitle = decodeURIComponent(options.title || '表单填写')
    this.nodeStatus = options.status || 'pending'
    this.leadId = options.leadId || ''
    this.flowId = options.flowId || '' // 流程ID
    this.level = options.level || '' // 节点层级
    this.nodeIndex = options.nodeIndex || '' // 主节点索引
    this.childIndex = options.childIndex || '' // 子节点索引
    this.realName = options.realName || '' // 负责人姓名
    this.duration = options.duration || '' // 时长
    this.parentNodeId = options.parentNodeId || '' // 父节点ID
    this.childNodeId = options.childNodeId || '' // 子节点ID

    // 详细打印每个参数
    console.log('- formType (表单类型):', this.formType)
    console.log('- nodeId (节点ID):', this.nodeId)
    console.log('- id (对应的ID):', this.id)
    console.log('- state (对应的状态):', this.state)
    console.log('- title (标题):', this.formTitle)
    console.log('- status (节点状态):', this.nodeStatus)
    console.log('- leadId (线索ID):', this.leadId)
    console.log('- flowId (流程ID):', this.flowId)
    console.log('- level (节点层级):', this.level)
    console.log('- nodeIndex (主节点索引):', this.nodeIndex)
    console.log('- childIndex (子节点索引):', this.childIndex)
    console.log('- realName (负责人姓名):', this.realName)
    console.log('- duration (时长):', this.duration)
    console.log('- parentNodeId (父节点ID):', this.parentNodeId)
    console.log('- childNodeId (子节点ID):', this.childNodeId)
    console.log('=== 参数打印完毕 ===')
    console.log('🔍 重要说明: id参数传递的是父节点ID:', this.id)

    this.loadFormConfig()
    this.loadFormData()
    this.loadFormDetail() // 新增：加载表单详情
  },
  methods: {
    // 加载表单配置
    async loadFormConfig() {
      // 模拟API调用，实际应该从后端获取表单配置
      try {
        // const response = await this.$api.getFormConfig(this.formType)
        // this.formConfig = response.data
        
        // 模拟数据
        this.formConfig = this.getMockFormConfig(this.formType)
        this.initFormData()
      } catch (error) {
        console.error('加载表单配置失败:', error)
        uni.showToast({
          title: '加载表单配置失败',
          icon: 'none'
        })
      }
    },

    // 加载表单详情
    loadFormDetail() {
      console.log('=== 开始调用 EstateWork/flowdetail 接口 ===')
      console.log('请求参数:')
      console.log('- 传入的id参数(父节点ID):', this.id)
      console.log('- 父节点ID:', this.parentNodeId)
      console.log('- 子节点ID:', this.childNodeId)
      console.log('- 说明: 接口使用的是父节点ID')

      // 调用 EstateWork/flowdetail 接口，使用传入的id作为参数
      this.$api.request('EstateWork/flowdetail', {
        id: this.id // 使用传入的id参数
      }, (res) => {
        console.log('=== EstateWork/flowdetail 接口返回结果 ===')
        console.log('完整响应对象:', res)
        console.log('响应状态:', res.status)
        console.log('响应信息:', res.info || '无')

        if (res.status === 'ok') {
          console.log('✅ 接口调用成功!')

          // 详细打印返回的数据结构
          if (res.data) {
            console.log('📋 返回的data字段:', res.data)
            console.log('📋 data字段类型:', typeof res.data)
            console.log('📋 data字段JSON格式:', JSON.stringify(res.data, null, 2))
          }

          if (res.flow) {
            console.log('📝 返回的flow字段:', res.flow)
            console.log('📝 flow字段类型:', typeof res.flow)
            console.log('📝 flow字段JSON格式:', JSON.stringify(res.flow, null, 2))
          }

          // 打印所有返回字段
          console.log('🔍 所有返回字段:')
          Object.keys(res).forEach(key => {
            console.log(`  - ${key}:`, res[key])
          })

          // 处理动态表单配置
          if (res.flow && Array.isArray(res.flow)) {
            console.log('🔧 开始处理动态表单配置')
            this.processDynamicFormConfig(res.flow)
          } else {
            console.log('⚠️ 没有找到flow数据，使用默认表单配置')
          }

        } else {
          console.log('❌ 接口调用失败:', res.info || '未知错误')
          uni.showToast({
            title: res.info || 'EstateWork/flowdetail 接口调用失败',
            icon: 'none',
            duration: 3000
          })
        }

        console.log('=== EstateWork/flowdetail 接口调用完毕 ===')
      }, (error) => {
        console.error('=== EstateWork/flowdetail 接口调用出错 ===')
        console.error('❌ 错误信息:', error)
        uni.showToast({
          title: 'EstateWork/flowdetail 接口调用出错',
          icon: 'none',
          duration: 3000
        })
      })
    },

    // 处理动态表单配置
    processDynamicFormConfig(flowData) {
      console.log('=== 开始处理动态表单配置 ===')
      console.log('原始flow数据:', flowData)

      // 查找匹配的节点（根据传入的父节点ID）
      const targetNode = flowData.find(node => node.id == this.id)

      if (!targetNode) {
        console.log('❌ 未找到匹配的节点，ID:', this.id)
        uni.showToast({
          title: '未找到对应的表单配置',
          icon: 'none'
        })
        return
      }

      console.log('✅ 找到匹配的节点:', targetNode)
      console.log('节点名称:', targetNode.work_name)
      console.log('节点状态:', targetNode.state)
      console.log('子表单项:', targetNode.children)

      // 更新表单标题
      this.formTitle = targetNode.work_name || this.formTitle

      // 处理子表单项
      if (targetNode.children && Array.isArray(targetNode.children)) {
        const dynamicFormConfig = this.convertToFormConfig(targetNode.children)
        console.log('转换后的表单配置:', dynamicFormConfig)

        // 更新表单配置
        this.formConfig = dynamicFormConfig

        // 重新初始化表单数据
        this.initFormData()

        console.log('✅ 动态表单配置已应用')
        uni.showToast({
          title: '表单配置已加载',
          icon: 'success'
        })
      } else {
        console.log('⚠️ 该节点没有子表单项')
        uni.showToast({
          title: '该节点没有表单项',
          icon: 'none'
        })
      }

      console.log('=== 动态表单配置处理完毕 ===')
    },

    // 将API返回的children数据转换为表单配置格式
    convertToFormConfig(children) {
      console.log('=== 开始转换表单配置 ===')

      const sections = [{
        title: '表单信息',
        fields: []
      }]

      children.forEach((child, index) => {
        console.log(`处理第${index + 1}个表单项:`, child)

        const field = {
          key: `field_${child.id}`, // 使用ID作为key
          label: child.work_name, // 使用work_name作为标签
          type: this.getFieldType(child.work_name), // 根据名称推断字段类型
          placeholder: `请输入${child.work_name}`,
          required: true,
          apiId: child.id, // 保存API中的ID
          fullName: child.full_name // 保存完整名称
        }

        // 根据字段名称设置特殊配置
        if (child.work_name.includes('照片') || child.work_name.includes('图')) {
          field.type = 'image'
          field.placeholder = `请上传${child.work_name}`
        } else if (child.work_name.includes('视频')) {
          field.type = 'video'
          field.placeholder = `请上传${child.work_name}`
        } else if (child.work_name.includes('姓名')) {
          field.type = 'input'
          field.inputType = 'text'
        } else if (child.work_name.includes('面宽') || child.work_name.includes('进深')) {
          field.type = 'number'
          field.placeholder = `请输入${child.work_name}（米）`
        }

        sections[0].fields.push(field)
        console.log(`已添加字段:`, field)
      })

      console.log('=== 表单配置转换完毕 ===')
      return { sections }
    },

    // 根据字段名称推断字段类型
    getFieldType(workName) {
      if (workName.includes('照片') || workName.includes('图')) {
        return 'image'
      } else if (workName.includes('视频')) {
        return 'video'
      } else if (workName.includes('面宽') || workName.includes('进深') || workName.includes('数量')) {
        return 'number'
      } else if (workName.includes('描述') || workName.includes('说明') || workName.includes('备注')) {
        return 'textarea'
      } else {
        return 'input'
      }
    },

    // 加载表单数据
    async loadFormData() {
      if (this.nodeStatus === 'completed' || this.nodeStatus === 'processing') {
        try {
          // const response = await this.$api.getFormData(this.nodeId)
          // this.formData = response.data
          
          // 模拟已填写的数据
          this.formData = this.getMockFormData(this.formType)
        } catch (error) {
          console.error('加载表单数据失败:', error)
        }
      }
    },
    
    // 初始化表单数据
    initFormData() {
      const data = {}
      this.formConfig.sections.forEach(section => {
        section.fields.forEach(field => {
          if (!data.hasOwnProperty(field.key)) {
            data[field.key] = field.defaultValue || ''
          }
        })
      })
      this.formData = { ...data, ...this.formData }
    },
    
    // 获取模拟表单配置
    getMockFormConfig(formType) {
      const configs = {
        requirement_survey: {
          sections: [
            {
              title: '客户基本信息',
              fields: [
                {
                  key: 'customerName',
                  label: '客户姓名',
                  type: 'input',
                  placeholder: '请输入客户姓名',
                  required: true
                },
                {
                  key: 'customerPhone',
                  label: '联系电话',
                  type: 'input',
                  inputType: 'number',
                  placeholder: '请输入联系电话',
                  required: true
                },
                {
                  key: 'customerType',
                  label: '客户类型',
                  type: 'picker',
                  placeholder: '请选择客户类型',
                  options: [
                    { label: '个人客户', value: 'personal' },
                    { label: '企业客户', value: 'enterprise' }
                  ],
                  required: true
                }
              ]
            },
            {
              title: '需求信息',
              fields: [
                {
                  key: 'requirements',
                  label: '具体需求',
                  type: 'textarea',
                  placeholder: '请详细描述客户需求',
                  required: true,
                  maxLength: 1000
                },
                {
                  key: 'budget',
                  label: '预算范围',
                  type: 'picker',
                  placeholder: '请选择预算范围',
                  options: [
                    { label: '5万以下', value: 'below_5w' },
                    { label: '5-10万', value: '5w_10w' },
                    { label: '10-20万', value: '10w_20w' },
                    { label: '20万以上', value: 'above_20w' }
                  ],
                  required: true
                },
                {
                  key: 'timeline',
                  label: '期望完成时间',
                  type: 'date',
                  placeholder: '请选择期望完成时间',
                  required: true
                }
              ]
            }
          ]
        },
        technical_solution: {
          sections: [
            {
              title: '技术方案概述',
              fields: [
                {
                  key: 'solutionName',
                  label: '方案名称',
                  type: 'input',
                  placeholder: '请输入技术方案名称',
                  required: true
                },
                {
                  key: 'technology',
                  label: '核心技术',
                  type: 'picker',
                  placeholder: '请选择核心技术',
                  options: [
                    { label: '物联网技术', value: 'iot' },
                    { label: '人工智能', value: 'ai' },
                    { label: '云计算', value: 'cloud' },
                    { label: '大数据', value: 'bigdata' }
                  ],
                  required: true
                },
                {
                  key: 'description',
                  label: '方案描述',
                  type: 'textarea',
                  placeholder: '请详细描述技术方案',
                  required: true,
                  maxLength: 2000
                }
              ]
            },
            {
              title: '实施计划',
              fields: [
                {
                  key: 'duration',
                  label: '实施周期',
                  type: 'picker',
                  placeholder: '请选择实施周期',
                  options: [
                    { label: '1-2周', value: '1_2_weeks' },
                    { label: '3-4周', value: '3_4_weeks' },
                    { label: '1-2个月', value: '1_2_months' },
                    { label: '3个月以上', value: 'above_3_months' }
                  ],
                  required: true
                },
                {
                  key: 'resources',
                  label: '所需资源',
                  type: 'textarea',
                  placeholder: '请描述所需的人力、物力资源',
                  required: true
                }
              ]
            }
          ]
        },
        quotation: {
          sections: [
            {
              title: '报价信息',
              fields: [
                {
                  key: 'projectName',
                  label: '项目名称',
                  type: 'input',
                  placeholder: '请输入项目名称',
                  required: true
                },
                {
                  key: 'totalAmount',
                  label: '总金额',
                  type: 'number',
                  placeholder: '请输入总金额（元）',
                  required: true
                },
                {
                  key: 'paymentMethod',
                  label: '付款方式',
                  type: 'picker',
                  placeholder: '请选择付款方式',
                  options: [
                    { label: '一次性付款', value: 'full_payment' },
                    { label: '分期付款', value: 'installment' },
                    { label: '按进度付款', value: 'progress_payment' }
                  ],
                  required: true
                }
              ]
            },
            {
              title: '费用明细',
              fields: [
                {
                  key: 'materialCost',
                  label: '材料费用',
                  type: 'number',
                  placeholder: '请输入材料费用（元）',
                  required: true
                },
                {
                  key: 'laborCost',
                  label: '人工费用',
                  type: 'number',
                  placeholder: '请输入人工费用（元）',
                  required: true
                },
                {
                  key: 'otherCost',
                  label: '其他费用',
                  type: 'number',
                  placeholder: '请输入其他费用（元）'
                },
                {
                  key: 'remarks',
                  label: '备注说明',
                  type: 'textarea',
                  placeholder: '请输入费用说明或备注',
                  maxLength: 500
                }
              ]
            }
          ]
        },
        risk_assessment: {
          sections: [
            {
              title: '风险识别',
              fields: [
                {
                  key: 'riskType',
                  label: '风险类型',
                  type: 'picker',
                  placeholder: '请选择主要风险类型',
                  options: [
                    { label: '技术风险', value: 'technical' },
                    { label: '进度风险', value: 'schedule' },
                    { label: '成本风险', value: 'cost' },
                    { label: '质量风险', value: 'quality' }
                  ],
                  required: true
                },
                {
                  key: 'riskLevel',
                  label: '风险等级',
                  type: 'picker',
                  placeholder: '请选择风险等级',
                  options: [
                    { label: '低风险', value: 'low' },
                    { label: '中风险', value: 'medium' },
                    { label: '高风险', value: 'high' }
                  ],
                  required: true
                },
                {
                  key: 'riskDescription',
                  label: '风险描述',
                  type: 'textarea',
                  placeholder: '请详细描述识别到的风险',
                  required: true,
                  maxLength: 1000
                }
              ]
            },
            {
              title: '应对措施',
              fields: [
                {
                  key: 'preventiveMeasures',
                  label: '预防措施',
                  type: 'textarea',
                  placeholder: '请描述预防风险的措施',
                  required: true
                },
                {
                  key: 'contingencyPlan',
                  label: '应急预案',
                  type: 'textarea',
                  placeholder: '请描述风险发生时的应急预案',
                  required: true
                }
              ]
            }
          ]
        }
      }

      return configs[formType] || { sections: [] }
    },
    
    // 获取模拟表单数据
    getMockFormData(formType) {
      const mockData = {
        requirement_survey: {
          customerName: '张先生',
          customerPhone: '13812345678',
          customerType: 'personal',
          requirements: '希望安装智能家居系统，包括智能照明、安防监控、环境控制等功能',
          budget: '10w_20w',
          timeline: '2024-03-01'
        },
        technical_solution: {
          solutionName: '智能家居集成方案',
          technology: 'iot',
          description: '基于物联网技术的智能家居解决方案，包括智能照明、安防监控、环境控制等子系统',
          duration: '1_2_months',
          resources: '需要技术工程师2名，项目经理1名，安装人员3名'
        },
        quotation: {
          projectName: '张先生智能家居项目',
          totalAmount: '150000',
          paymentMethod: 'progress_payment',
          materialCost: '80000',
          laborCost: '50000',
          otherCost: '20000',
          remarks: '包含设备采购、安装调试、培训服务等'
        },
        risk_assessment: {
          riskType: 'technical',
          riskLevel: 'medium',
          riskDescription: '客户对智能家居技术了解有限，可能在使用过程中遇到操作困难',
          preventiveMeasures: '提供详细的用户手册和操作培训，建立客服支持体系',
          contingencyPlan: '安排技术人员定期回访，提供远程技术支持服务'
        }
      }

      return mockData[formType] || {}
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待开启',
        'processing': '进行中',
        'completed': '已完成',
        'disabled': '禁用'
      }
      return statusMap[status] || '未知'
    },

    // 选择器变化处理
    handlePickerChange(event, fieldKey) {
      const index = event.detail.value
      const field = this.findFieldByKey(fieldKey)
      if (field && field.options && field.options[index]) {
        this.formData[fieldKey] = field.options[index].value
      }
    },

    // 日期变化处理
    handleDateChange(event, fieldKey) {
      this.formData[fieldKey] = event.detail.value
    },

    // 获取选择器显示文本
    getPickerDisplayText(field, value) {
      if (!value || !field.options) return ''
      const option = field.options.find(opt => opt.value === value)
      return option ? option.label : ''
    },

    // 根据key查找字段配置
    findFieldByKey(key) {
      for (let section of this.formConfig.sections) {
        for (let field of section.fields) {
          if (field.key === key) {
            return field
          }
        }
      }
      return null
    },

    // 表单验证
    validateForm() {
      for (let section of this.formConfig.sections) {
        for (let field of section.fields) {
          if (field.required && !this.formData[field.key]) {
            uni.showToast({
              title: `请填写${field.label}`,
              icon: 'none'
            })
            return false
          }
        }
      }
      return true
    },

    // 提交表单
    async handleSubmit() {
      if (!this.validateForm()) {
        return
      }

      try {
        uni.showLoading({
          title: '提交中...'
        })

        // 模拟API调用
        // await this.$api.submitForm({
        //   nodeId: this.nodeId,
        //   formType: this.formType,
        //   leadId: this.leadId,
        //   formData: this.formData
        // })

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        console.error('提交表单失败:', error)
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      }
    },

    // 选择图片
    chooseImage(fieldKey) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          console.log('选择图片成功:', res)
          this.formData[fieldKey] = res.tempFilePaths[0]
          // 这里可以添加上传到服务器的逻辑
          // this.uploadImage(res.tempFilePaths[0], fieldKey)
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 预览图片
    previewImage(imagePath) {
      uni.previewImage({
        urls: [imagePath],
        current: imagePath
      })
    },

    // 删除图片
    removeImage(fieldKey) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData[fieldKey] = ''
          }
        }
      })
    },

    // 选择视频
    chooseVideo(fieldKey) {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        maxDuration: 60,
        camera: 'back',
        success: (res) => {
          console.log('选择视频成功:', res)
          this.formData[fieldKey] = res.tempFilePath
          // 这里可以添加上传到服务器的逻辑
          // this.uploadVideo(res.tempFilePath, fieldKey)
        },
        fail: (err) => {
          console.error('选择视频失败:', err)
          uni.showToast({
            title: '选择视频失败',
            icon: 'none'
          })
        }
      })
    },

    // 删除视频
    removeVideo(fieldKey) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个视频吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData[fieldKey] = ''
          }
        }
      })
    },

    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  &.status-pending {
    background-color: #fff7e6;
  }

  &.status-processing {
    background-color: #e6f3ff;
  }

  &.status-completed {
    background-color: #e8f5e8;
  }
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;

  .status-pending & {
    color: #ff9500;
  }

  .status-processing & {
    color: #007aff;
  }

  .status-completed & {
    color: #34c759;
  }
}

.form-container {
  padding: 20rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e5e5e5;
}

.form-fields {
  padding: 30rpx;
}

.field-item {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.field-wrapper {
  display: flex;
  flex-direction: column;
}

.field-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required-mark {
  color: #ff3b30;
  margin-left: 4rpx;
}

.field-input {
  padding: 24rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;

  &:focus {
    border-color: #007aff;
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #999999;
  }
}

.field-textarea {
  padding: 24rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  min-height: 120rpx;

  &:focus {
    border-color: #007aff;
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #999999;
  }
}

.field-picker {
  padding: 24rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-text {
  font-size: 28rpx;
  color: #333333;
  flex: 1;

  &.placeholder {
    color: #999999;
  }
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  padding: 28rpx;
  border-radius: 8rpx;
  text-align: center;

  &:active {
    opacity: 0.8;
  }
}

.primary-btn {
  background-color: #007aff;
}

.secondary-btn {
  background-color: #8e8e93;
}

.success-btn {
  background-color: #34c759;
}

.btn-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 图片上传样式 */
.image-upload-container {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
}

.image-actions {
  display: flex;
  justify-content: space-between;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e5e5e5;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  color: #007aff;
  background-color: #e6f3ff;

  &.delete-btn {
    color: #ff3b30;
    background-color: #ffe6e6;
  }

  &:active {
    opacity: 0.8;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  background-color: #f8f9fa;
  border: 2rpx dashed #d1d1d6;

  &:active {
    background-color: #f0f0f0;
  }
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
}

/* 视频上传样式 */
.video-upload-container {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  overflow: hidden;
}

.video-preview {
  position: relative;
}

.preview-video {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
}

.video-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e5e5e5;
}
</style>
